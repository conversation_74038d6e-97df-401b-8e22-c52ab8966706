<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction;

use Exception;
use Carbon\Carbon;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CanCreateRequestIfHaveMultipleSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\BuildParamTaoYeuCauTuDongTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\ThucHienTaoLenhTrichTuDongTask;

class CreateRequestAutoBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$canRunJob = parent::canRunJob();

		if (!$canRunJob) {
			return 'job will for nextday';
		}

		// Lấy danh sách contract_code cần xử lý (distinct để tránh trùng lặp)
		$contractCodes = CollectDebtSchedule::query()
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->where('rundate', '<=', now()->format('Ymd'))
			->whereNotExists(function ($query) {
				$query->selectRaw("1")
							->from('debt_recovery_processing as p')
							->whereColumn('p.contract_code', 'debt_recovery_contract_plan.contract_code');
			})
			->distinct()
			->pluck('contract_code')
			->toArray();

		if (empty($contractCodes)) {
			return ['msg' => 'No contract codes to process'];
		}

		// Chia contract codes thành các batch để đảm bảo 1 contract chỉ thuộc 1 batch
		$contractBatches = $this->createContractBatches($contractCodes);

		$processResult = $this->processBatch($contractBatches);
		return $processResult;
	}

	/**
	 * Chia danh sách contract codes thành các batch
	 * Đảm bảo mỗi contract chỉ thuộc 1 batch duy nhất
	 */
	private function createContractBatches(array $contractCodes): array
	{
		$batches = [];
		$currentBatch = [];
		$batchIndex = 0;

		foreach ($contractCodes as $contractCode) {
			$currentBatch[] = $contractCode;

			// Nếu batch hiện tại đã đủ kích thước hoặc đã đạt số lượng batch tối đa
			if (count($currentBatch) >= $this->batchSize) {
				$batches[] = $currentBatch;
				$currentBatch = [];
				$batchIndex++;

				// Giới hạn số lượng batch để tránh quá tải
				if ($batchIndex >= $this->batchCount) {
					break;
				}
			}
		}

		// Thêm batch cuối cùng nếu còn contract codes
		if (!empty($currentBatch) && $batchIndex < $this->batchCount) {
			$batches[] = $currentBatch;
		}

		return $batches;
	}

	private function processBatch($contractBatches)
	{
		$client = parent::createHttpClient($this->timeout-10);

		$baseUrl = config('app.url');

		$requests = function () use ($contractBatches, $baseUrl) {
			foreach ($contractBatches as $batchIndex => $contractCodes) {
				$contractCodesParam = implode(',', $contractCodes);
				$url = sprintf('%s/HandleCreateRequestAutoBatch?contracts=%s', $baseUrl, urlencode($contractCodesParam));
				yield $batchIndex => new Request('GET', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $batchIndex) {
				// $body = (string)$response->getBody();
				Log::info("[CreateRequest] Batch $batchIndex completed successfully");
			},
			'rejected' => function (\Throwable $reason, $batchIndex) {
				$msg = "[CreateRequest --->batch $batchIndex] failed: " . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}


	public function HandleCreateRequestAuto()
	{
		$min = request()->get('min');
		$max = request()->get('max');

		$plans = CollectDebtPlan::query()
						->whereBetween('id', [$min, $max])
						->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
						->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
						->where('rundate', '<=', now()->format('Ymd'))
						->select(['contract_code', 'profile_id'])
						->get();

		if ($plans->isEmpty()) {
			return 'No data';
		}

		foreach ($plans as $plan) {
			$this->HandleRecord($plan->contract_code, $plan->profile_id);
		}
	}

	/**
	 * Xử lý batch contract codes mới
	 */
	public function HandleCreateRequestAutoBatch()
	{
		$contractCodesParam = request()->get('contracts');

		if (empty($contractCodesParam)) {
			return 'No contract codes provided';
		}

		$contractCodes = explode(',', urldecode($contractCodesParam));

		// Lấy thông tin profile_id cho từng contract
		$plans = CollectDebtSchedule::query()
						->whereIn('contract_code', $contractCodes)
						->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
						->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
						->where('rundate', '<=', now()->format('Ymd'))
						->select(['contract_code', 'profile_id'])
						->distinct()
						->get();

		if ($plans->isEmpty()) {
			return 'No data for provided contracts';
		}

		foreach ($plans as $plan) {
			try {
				$this->HandleRecord($plan->contract_code, $plan->profile_id);
			} catch (\Throwable $th) {
				Log::error("[HandleCreateRequestAutoBatch] Error processing contract {$plan->contract_code}: " . $th->getMessage());
				// Tiếp tục xử lý các contract khác
				continue;
			}
		}

		return 'Batch processing completed';
	}

	public function HandleRecord(string $contractCode, $profileId)
	{
		$canCreateMultiple = app(CanCreateRequestIfHaveMultipleSubAction::class)->run($profileId, $contractCode);
		if (!$canCreateMultiple) {
			return $contractCode;
		}

		$listContractExclude = $this->getListHopDongCanNe();

		// 1. Đang bị exclude thì không cho tạo lệnh
		if (in_array($contractCode, $listContractExclude)) {
			return $contractCode;
		}

		// 2. Chỉ lấy ra các lịch có `is_process` là CHƯA XỬ LÝ
		$plan = CollectDebtSchedule::query()
			->where('contract_code', $contractCode)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->where('rundate', '<=', now()->format('Ymd'))
			->orderByRaw('rundate ASC, cycle_number ASC, type ASC, is_settlement ASC')
			->first();

		if (!$plan) {
			// Không có plan thì kết thúc và giải phóng luồng luôn
			return $contractCode;
		}

		// 2. Update lịch vừa select lên thành đang xử lý ---> để thực hiện xử lý
		$wasUpdateProcessing = CollectDebtSchedule::query()
			->where('contract_code', $plan->contract_code)
			->where('id', $plan->id)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->update([
				'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
			]);

		if (!$wasUpdateProcessing) {
			$msg = "[ErrorUpdateProcessing---->$contractCode]" . 'Loi khong the update len thanh dang xu ly';
			Log::info($msg, ['planId' => $plan->id]);
			throw new Exception($msg);
		}


		// 3. 
		// Cập nhật các bản ghi lịch thu có cùng rundate về trạng thái ĐANG XỬ LÝ
		// Không thể biết có lịch quá khứ hay không, nên chỉ bắt catch
		try {
			$updatedPlanRows = CollectDebtSchedule::query()
				->where('contract_code', $plan->contract_code)
				->where('rundate', $plan->rundate)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
				->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
				->where('id', '!=', $plan->id)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
				]);
		} catch (\Throwable $th) {
			Log::info("[ErrorUpdateProcessing---->$contractCode]" . 'Loi update cac lich thu cung rundate', ['details' => Helper::traceError($th)]);

			// Doan nay phai update plan hien tai =>  ve da chua xu ly
			$updatedVeChuaXuLy = CollectDebtSchedule::query()->where('id', $plan->id)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
				]);

			throw $th;
		}


		$listLichThuRefresh = CollectDebtSchedule::query()
			->join('debt_recovery_share', 'debt_recovery_share.contract_code', '=', 'debt_recovery_contract_plan.contract_code')
			->where('debt_recovery_contract_plan.contract_code', $plan->contract_code)
			->where('debt_recovery_contract_plan.rundate', $plan->rundate)
			->where('debt_recovery_contract_plan.is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
			->where('debt_recovery_contract_plan.status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->select([
				'debt_recovery_contract_plan.*',

				// Share
				'debt_recovery_share.partner_code',
				'debt_recovery_share.payment_guide'
			])
			->get();


		$isToanBoLichLaDangXuLy = $listLichThuRefresh->every(function (CollectDebtSchedule $p) {
			return $p->is_process == CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY;
		});

		// Toàn bộ các lịch phải là ĐANG XỬ LÝ --> thì mới có thể tạo yêu cầu được
		throw_if(!$isToanBoLichLaDangXuLy, new Exception('Toan bo lich chua ve dang xu ly'));

		$listLichThuTaoYeuCau = $listLichThuRefresh;

		$listLichThuTaoYeuCau = app(PlanSortableCollectionByRule::class)->sortCollection($listLichThuTaoYeuCau);

		$partnerCode = $listLichThuRefresh->first()->partner_code;
		
		// 5. Start transaction
		DB::beginTransaction();
		try {
			$buildParamTaoYeuCau = app(BuildParamTaoYeuCauTuDongTask::class)->run($listLichThuTaoYeuCau);
			$collectDebtRequest = app(ThucHienTaoLenhTrichTuDongTask::class)->run($buildParamTaoYeuCau, $listLichThuTaoYeuCau);

			$collectDebtProcessing = CollectDebtProcessing::query()->forceCreate([
				'contract_code' => $collectDebtRequest->contract_code,
				'partner_request_id' => $collectDebtRequest->partner_request_id,
				'expired_at' => Carbon::createFromTimestamp($collectDebtRequest->time_expired),
				'created_at' => now(),
				'updated_at' => now(),
				'checked_at' => now()->addMinutes(60),
				'push_time' => Helper::getSoLanVay($collectDebtRequest->contract_code, $partnerCode),
				'profile_id' => $profileId
			]);

			if (!$collectDebtProcessing) {
				throw new Exception('Loi khong tao duoc ban ghi processing...');
			}


			DB::commit();


			return $collectDebtRequest->partner_request_id;
		} catch (\Throwable $th) {
			DB::rollBack();
			Log::info("[ErrorTaoYeuCau----> $contractCode]", ['details' => Helper::traceError($th)]);

			// Update toàn bộ lịch thu cần tạo yc về is_process: CHƯA XỬ LÝ
			$updated = CollectDebtSchedule::query()
				->where('contract_code', $plan->contract_code)
				->whereIn('id', $listLichThuTaoYeuCau->pluck('id')->toArray())
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
				]);

			if (!$updated) {
				throw new Exception('Loi rollback trang thai, can sua tay');
			}
		}
	}

	public function getListHopDongCanNe(): array
	{
		$listHopDongDungJob = CollectDebtConfigAuto::getHopDongDungJobCache();
		return $listHopDongDungJob;
	}
} // End class