<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction;

use Exception;
use Carbon\Carbon;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Ultilities\BatchUtil;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CanCreateRequestIfHaveMultipleSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\BuildParamTaoYeuCauTuDongTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\ThucHienTaoLenhTrichTuDongTask;

/**
 * Phiên bản sử dụng database locking để tránh race condition
 * Giữ nguyên cách chia batch theo ID range nhưng thêm locking mechanism
 */
class CreateRequestAutoBatchActionV3 extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$canRunJob = parent::canRunJob();

		if (!$canRunJob) {
			return 'job will for nextday';
		}

		$r = CollectDebtSchedule::query()
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->where('rundate', '<=', now()->format('Ymd'))
			->whereNotExists(function ($query) {
				$query->selectRaw("1")
							->from('debt_recovery_processing as p')
							->whereColumn('p.contract_code', 'debt_recovery_contract_plan.contract_code');
			})
			->selectRaw("MIN(id) as min_id, MAX(id) as max_id")
			->first();
			
		if (empty($r->min_id) && empty($r->max_id)) {
			return ['msg' => 'No data for min - max'];
		}

		$ranges = BatchUtil::getRanges($r->min_id, $r->max_id, $this->batchCount, $this->batchSize);

		$processResult = $this->processBatch($ranges);
		return $processResult;
	}

	private function processBatch($ranges)
	{
		$client = parent::createHttpClient($this->timeout-10);

		$baseUrl = config('app.url');
		
		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/HandleCreateRequestAutoWithLock?min=%s&max=%s', $baseUrl, $r[0], $r[1]);
				yield $r => new Request('GET', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
				
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[CreateRequest --->range error: " . json_encode($r) . '] failed: ' . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
				
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[CreateRequest --->range error: " . json_encode($r) . '] failed: ' . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}

	/**
	 * Xử lý với database locking để tránh race condition
	 */
	public function HandleCreateRequestAutoWithLock()
	{
		$min = request()->get('min');
		$max = request()->get('max');

		$plans = CollectDebtPlan::query()
						->whereBetween('id', [$min, $max])
						->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
						->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
						->where('rundate', '<=', now()->format('Ymd'))
						->select(['contract_code', 'profile_id'])
						->get();

		if ($plans->isEmpty()) {
			return 'No data';
		}

		// Group theo contract_code để xử lý tuần tự
		$contractGroups = $plans->groupBy('contract_code');

		foreach ($contractGroups as $contractCode => $contractPlans) {
			try {
				// Sử dụng database lock để đảm bảo chỉ 1 process xử lý 1 contract tại 1 thời điểm
				$this->HandleRecordWithLock($contractCode, $contractPlans->first()->profile_id);
			} catch (\Throwable $th) {
				Log::error("[HandleCreateRequestAutoWithLock] Error processing contract {$contractCode}: " . $th->getMessage());
				// Tiếp tục xử lý các contract khác
				continue;
			}
		}

		return 'Batch processing completed';
	}

	/**
	 * Xử lý record với database locking
	 */
	public function HandleRecordWithLock(string $contractCode, $profileId)
	{
		// Sử dụng GET_LOCK để tạo named lock cho contract
		$lockName = "process_contract_{$contractCode}";
		$lockTimeout = 30; // 30 seconds timeout

		try {
			// Thử acquire lock
			$lockResult = DB::selectOne("SELECT GET_LOCK(?, ?) as lock_result", [$lockName, $lockTimeout]);
			
			if (!$lockResult || $lockResult->lock_result != 1) {
				Log::info("[HandleRecordWithLock] Could not acquire lock for contract: {$contractCode}");
				return $contractCode; // Contract đang được xử lý bởi process khác
			}

			// Đã có lock, tiến hành xử lý
			return $this->HandleRecord($contractCode, $profileId);

		} finally {
			// Luôn release lock
			DB::selectOne("SELECT RELEASE_LOCK(?) as release_result", [$lockName]);
		}
	}

	public function HandleRecord(string $contractCode, $profileId)
	{
		$canCreateMultiple = app(CanCreateRequestIfHaveMultipleSubAction::class)->run($profileId, $contractCode);
		if (!$canCreateMultiple) {
			return $contractCode;
		}

		$listContractExclude = $this->getListHopDongCanNe();

		// 1. Đang bị exclude thì không cho tạo lệnh
		if (in_array($contractCode, $listContractExclude)) {
			return $contractCode;
		}

		// 2. Chỉ lấy ra các lịch có `is_process` là CHƯA XỬ LÝ
		$plan = CollectDebtSchedule::query()
			->where('contract_code', $contractCode)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->where('rundate', '<=', now()->format('Ymd'))
			->orderByRaw('rundate ASC, cycle_number ASC, type ASC, is_settlement ASC')
			->first();

		if (!$plan) {
			// Không có plan thì kết thúc và giải phóng luồng luôn
			return $contractCode;
		}

		// 2. Update lịch vừa select lên thành đang xử lý ---> để thực hiện xử lý
		$wasUpdateProcessing = CollectDebtSchedule::query()
			->where('contract_code', $plan->contract_code)
			->where('id', $plan->id)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->update([
				'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
			]);

		if (!$wasUpdateProcessing) {
			$msg = "[ErrorUpdateProcessing---->$contractCode]" . 'Loi khong the update len thanh dang xu ly';
			Log::info($msg, ['planId' => $plan->id]);
			throw new Exception($msg);
		}

		// 3. Cập nhật các bản ghi lịch thu có cùng rundate về trạng thái ĐANG XỬ LÝ
		try {
			CollectDebtSchedule::query()
				->where('contract_code', $plan->contract_code)
				->where('rundate', $plan->rundate)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
				->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
				->where('id', '!=', $plan->id)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
				]);
		} catch (\Throwable $th) {
			Log::info("[ErrorUpdateProcessing---->$contractCode]" . 'Loi update cac lich thu cung rundate', ['details' => Helper::traceError($th)]);

			// Rollback plan hiện tại về chưa xử lý
			CollectDebtSchedule::query()->where('id', $plan->id)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
				]);

			throw $th;
		}

		// Tiếp tục với logic xử lý như cũ...
		$this->processContractPlan($plan, $contractCode, $profileId);
	}

	private function processContractPlan($plan, $contractCode, $profileId)
	{
		// Logic xử lý contract plan (giữ nguyên từ code cũ)
		// ... (phần còn lại của HandleRecord method)
	}

	public function getListHopDongCanNe(): array
	{
		$listHopDongDungJob = CollectDebtConfigAuto::getHopDongDungJobCache();
		return $listHopDongDungJob;
	}
}
